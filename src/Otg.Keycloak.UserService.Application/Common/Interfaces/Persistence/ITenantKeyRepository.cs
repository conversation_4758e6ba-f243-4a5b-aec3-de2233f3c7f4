using ErrorOr;
using Otg.Hcm.Domain.ValueObjects;
using Otg.Keycloak.UserService.Domain.Aggregates;
using Otg.Keycloak.UserService.Domain.ValueObjects;

namespace Otg.Keycloak.UserService.Application.Common.Interfaces.Persistence;

/// <summary>
/// Repository for managing tenant RSA keys
/// </summary>
public interface ITenantKeyRepository
{
    /// <summary>
    /// Inserts a new tenant key
    /// </summary>
    Task<ErrorOr<TenantKey>> InsertKeyAsync(TenantKey tenantKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing tenant key
    /// </summary>
    Task<ErrorOr<TenantKey>> UpdateKeyAsync(TenantKey tenantKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Upserts a tenant key (creates if not exists, updates if exists)
    /// </summary>
    Task<ErrorOr<TenantKey>> UpsertKeyAsync(TenantKey tenantKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the key for a tenant
    /// </summary>
    ErrorOr<TenantKey?> GetKeyForTenantAsync(string tenantId, CancellationToken cancellationToken = default);
}
