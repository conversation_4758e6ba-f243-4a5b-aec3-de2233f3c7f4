using Otg.Keycloak.UserService.Domain.Aggregates;
using Otg.Keycloak.UserService.Domain.ValueObjects;
using ErrorOr;
using Otg.Keycloak.UserService.Infrastructure.Persistence.MongoDb.Repositories;
using Otg.Hcm.Infrastructure.Persistence.MongoDb;
using Otg.Hcm.Infrastructure.Persistence.MongoDb.Builders;
using Otg.Keycloak.UserService.Application.Common.Errors;
using MongoDB.Driver;
using MongoDB.Bson;
using Otg.Hcm.Domain.ValueObjects;
using System.Net;
using System.Linq.Expressions;

namespace Otg.Keycloak.UserService.Infrastructure.Persistence.MongoDb.UnitTests.Repositories;

public class TenantKeyRepositoryTests
{
    #region Test Data Builder

    private class TenantKeyBuilder
    {
        private string _tenantId = "test-tenant-123";
        private string _publicKey = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----";
        private string _encryptedPrivateKey = "encrypted-private-key-data";
        private DateTime _createdAt = DateTime.UtcNow.AddDays(-1);
        private DateTime _updatedAt = DateTime.UtcNow;

        public TenantKeyBuilder WithTenantId(string tenantId) { _tenantId = tenantId; return this; }
        public TenantKeyBuilder WithPublicKey(string key) { _publicKey = key; return this; }
        public TenantKeyBuilder WithEncryptedPrivateKey(string key) { _encryptedPrivateKey = key; return this; }
        public TenantKeyBuilder WithCreatedAt(DateTime dt) { _createdAt = dt; return this; }
        public TenantKeyBuilder WithUpdatedAt(DateTime dt) { _updatedAt = dt; return this; }

        public TenantKey Build() => new TenantKey
        {
            Id = TenantKeyId.Create(),
            TenantId = new TenantId { Value = _tenantId },
            PublicKey = _publicKey,
            EncryptedPrivateKey = _encryptedPrivateKey,
            CreatedAt = _createdAt,
            UpdatedAt = _updatedAt
        };
    }

    #endregion

    #region Helpers

    private static TenantKeyRepository GetRepository(IHcmRepository<TenantKey, TenantKeyId> mockRepository)
    {
        var mockRepositoryFactory = Substitute.For<IHcmRepositoryFactory>();
        mockRepositoryFactory.CreateMultiTenantRepository<TenantKey, TenantKeyId>(Arg.Any<DuplicateKeyHandlerDelegate?>())
            .Returns(mockRepository);
        return new TenantKeyRepository(mockRepositoryFactory);
    }

    private static IHcmRepository<TenantKey, TenantKeyId> MockRepositoryWithKeys(params TenantKey[] keys)
    {
        var repo = Substitute.For<IHcmRepository<TenantKey, TenantKeyId>>();
        repo.GetAllAggregateRootsAsQueryable().Returns(keys.AsQueryable());
        return repo;
    }

    #endregion

    #region InsertKeyAsync Tests

    [Fact]
    public async Task InsertKeyAsync_WithValidTenantKey_ShouldCreateNewKeyWithTimestampsAndNewId()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder()
            .WithTenantId("test-tenant-123")
            .WithPublicKey("-----BEGIN PUBLIC KEY-----\nTEST_KEY_DATA\n-----END PUBLIC KEY-----")
            .WithEncryptedPrivateKey("test-encrypted-private-key")
            .Build();
        var originalId = tenantKey.Id;
        var mockRepository = MockRepositoryWithKeys();
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>()).Returns(tenantKey);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.InsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeFalse();
        result.Value.Should().NotBeNull();
        result.Value.TenantId.Value.Should().Be("test-tenant-123");
        result.Value.PublicKey.Should().Be("-----BEGIN PUBLIC KEY-----\nTEST_KEY_DATA\n-----END PUBLIC KEY-----");
        result.Value.EncryptedPrivateKey.Should().Be("test-encrypted-private-key");
        tenantKey.Id.Should().NotBe(originalId);
        tenantKey.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        tenantKey.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        await mockRepository.Received(1).AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task InsertKeyAsync_WhenRepositoryFails_ShouldReturnCreateFailedError()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys();
        var createError = Error.Failure("Database.CreateFailed", "Failed to create entity");
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>()).Returns(createError);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.InsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.CreateFailed");
        result.FirstError.Description.Should().Contain("Database.CreateFailed");
    }

    [Fact]
    public async Task InsertKeyAsync_WhenDatabaseExceptionThrown_ShouldReturnCreateFailedError()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys();
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromException<ErrorOr<TenantKey>>(new MongoException("Database connection failed")));
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.InsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.CreateFailed");
        result.FirstError.Description.Should().Contain("MongoException");
    }

    [Fact]
    public async Task InsertKeyAsync_WithNullTenantKey_ShouldReturnError()
    {
        // Arrange
        TenantKey tenantKey = null!;
        var mockRepository = MockRepositoryWithKeys();
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.InsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.CreateFailed");
    }

    [Fact]
    public async Task InsertKeyAsync_WithCancellationToken_ShouldPassTokenToRepository()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys();
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>()).Returns(tenantKey);
        var repository = GetRepository(mockRepository);
        var cancellationToken = new CancellationToken();

        // Act
        var result = await repository.InsertKeyAsync(tenantKey, cancellationToken);

        // Assert
        result.IsError.Should().BeFalse();
        await mockRepository.Received(1).AddAggregateRootV2Async(Arg.Any<TenantKey>(), cancellationToken);
    }

    [Fact]
    public async Task InsertKeyAsync_WhenUnexpectedExceptionThrown_ShouldReturnCreateFailedError()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys();
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromException<ErrorOr<TenantKey>>(new InvalidOperationException("Unexpected error")));
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.InsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.CreateFailed");
        result.FirstError.Description.Should().Contain("InvalidOperationException");
    }

    #endregion

    #region UpdateKeyAsync Tests

    [Fact]
    public async Task UpdateKeyAsync_WithExistingTenantKey_ShouldUpdateKey()
    {
        // Arrange
        var existingKey = new TenantKeyBuilder().Build();
        var updatedKey = new TenantKeyBuilder()
            .WithTenantId(existingKey.TenantId.Value)
            .WithPublicKey("-----BEGIN PUBLIC KEY-----\nUPDATED_KEY_DATA\n-----END PUBLIC KEY-----")
            .WithEncryptedPrivateKey("updated-encrypted-private-key")
            .Build();

        var mockRepository = MockRepositoryWithKeys(existingKey);
        var mockUpdateBuilder = Substitute.For<IHcmAggregateRootUpdateBuilder<TenantKey, TenantKeyId>>();
        mockRepository.AggregateRootUpdateBuilder(existingKey.Id).Returns(mockUpdateBuilder);
        mockRepository.UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>()).Returns(updatedKey);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpdateKeyAsync(updatedKey);

        // Assert
        result.IsError.Should().BeFalse();
        result.Value.Should().NotBeNull();
        mockRepository.Received(1).AggregateRootUpdateBuilder(existingKey.Id);
        await mockRepository.Received(1).UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task UpdateKeyAsync_WithNonExistentTenantKey_ShouldReturnNotFoundError()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys(); // Empty repository
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpdateKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Type.Should().Be(ErrorType.NotFound);
        result.FirstError.Code.Should().Be("TenantKey.NotFound");
        result.FirstError.Description.Should().Contain(tenantKey.TenantId.Value);
    }

    [Fact]
    public async Task UpdateKeyAsync_ShouldSetUpdatedAt()
    {
        // Arrange
        var existingKey = new TenantKeyBuilder().Build();
        var updatedKey = new TenantKeyBuilder().WithTenantId(existingKey.TenantId.Value).Build();
        var mockRepository = MockRepositoryWithKeys(existingKey);
        var mockUpdateBuilder = Substitute.For<IHcmAggregateRootUpdateBuilder<TenantKey, TenantKeyId>>();
        mockRepository.AggregateRootUpdateBuilder(existingKey.Id).Returns(mockUpdateBuilder);
        mockRepository.UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>()).Returns(updatedKey);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpdateKeyAsync(updatedKey);

        // Assert
        result.IsError.Should().BeFalse();
        updatedKey.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        mockUpdateBuilder.Received(1).Set(Arg.Any<Expression<Func<TenantKey, DateTime>>>(), Arg.Any<DateTime>());
    }

    [Fact]
    public async Task UpdateKeyAsync_WhenRepositoryUpdateFails_ShouldReturnUpdateFailedError()
    {
        // Arrange
        var existingKey = new TenantKeyBuilder().Build();
        var updatedKey = new TenantKeyBuilder().WithTenantId(existingKey.TenantId.Value).Build();
        var mockRepository = MockRepositoryWithKeys(existingKey);
        var mockUpdateBuilder = Substitute.For<IHcmAggregateRootUpdateBuilder<TenantKey, TenantKeyId>>();
        mockRepository.AggregateRootUpdateBuilder(existingKey.Id).Returns(mockUpdateBuilder);
        var updateError = Error.Failure("Database.UpdateFailed", "Failed to update entity");
        mockRepository.UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>()).Returns(updateError);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpdateKeyAsync(updatedKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.UpdateFailed");
        result.FirstError.Description.Should().Contain("Database.UpdateFailed");
    }

    [Fact]
    public async Task UpdateKeyAsync_WhenGetKeyForTenantFails_ShouldReturnError()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = Substitute.For<IHcmRepository<TenantKey, TenantKeyId>>();
        mockRepository.When(x => x.GetAllAggregateRootsAsQueryable())
            .Do(x => throw new InvalidOperationException("Database error"));
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpdateKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Should().Be(Errors.TenantKeyOperations.TenantKeyNotFound);
    }

    [Fact]
    public async Task UpdateKeyAsync_WhenUnexpectedExceptionThrown_ShouldReturnUpdateFailedError()
    {
        // Arrange
        var existingKey = new TenantKeyBuilder().Build();
        var updatedKey = new TenantKeyBuilder().WithTenantId(existingKey.TenantId.Value).Build();
        var mockRepository = MockRepositoryWithKeys(existingKey);
        var mockUpdateBuilder = Substitute.For<IHcmAggregateRootUpdateBuilder<TenantKey, TenantKeyId>>();
        mockRepository.AggregateRootUpdateBuilder(existingKey.Id).Returns(mockUpdateBuilder);
        mockRepository.UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>())
            .Returns(Task.FromException<ErrorOr<TenantKey>>(new InvalidOperationException("Unexpected error")));
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpdateKeyAsync(updatedKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.UpdateFailed");
        result.FirstError.Description.Should().Contain("InvalidOperationException");
    }

    [Fact]
    public async Task UpdateKeyAsync_WithValidCancellationToken_ShouldPassTokenToRepository()
    {
        // Arrange
        var existingKey = new TenantKeyBuilder().Build();
        var updatedKey = new TenantKeyBuilder().WithTenantId(existingKey.TenantId.Value).Build();
        var mockRepository = MockRepositoryWithKeys(existingKey);
        var mockUpdateBuilder = Substitute.For<IHcmAggregateRootUpdateBuilder<TenantKey, TenantKeyId>>();
        mockRepository.AggregateRootUpdateBuilder(existingKey.Id).Returns(mockUpdateBuilder);
        mockRepository.UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>()).Returns(updatedKey);
        var repository = GetRepository(mockRepository);
        var cancellationToken = new CancellationToken();

        // Act
        var result = await repository.UpdateKeyAsync(updatedKey, cancellationToken);

        // Assert
        result.IsError.Should().BeFalse();
        await mockRepository.Received(1).UpdateAggregateRootAsync(mockUpdateBuilder, cancellationToken);
    }

    [Fact]
    public async Task UpdateKeyAsync_ShouldUpdateAllKeyProperties()
    {
        // Arrange
        var existingKey = new TenantKeyBuilder().Build();
        var updatedKey = new TenantKeyBuilder()
            .WithTenantId(existingKey.TenantId.Value)
            .WithPublicKey("-----BEGIN PUBLIC KEY-----\nNEW_PUBLIC_KEY\n-----END PUBLIC KEY-----")
            .WithEncryptedPrivateKey("new-encrypted-private-key")
            .Build();

        var mockRepository = MockRepositoryWithKeys(existingKey);
        var mockUpdateBuilder = Substitute.For<IHcmAggregateRootUpdateBuilder<TenantKey, TenantKeyId>>();
        mockRepository.AggregateRootUpdateBuilder(existingKey.Id).Returns(mockUpdateBuilder);
        mockRepository.UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>()).Returns(updatedKey);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpdateKeyAsync(updatedKey);

        // Assert
        result.IsError.Should().BeFalse();
        mockUpdateBuilder.Received(1).Set(Arg.Any<Expression<Func<TenantKey, string?>>>(), updatedKey.PublicKey);
        mockUpdateBuilder.Received(1).Set(Arg.Any<Expression<Func<TenantKey, string?>>>(), updatedKey.EncryptedPrivateKey);
        mockUpdateBuilder.Received(1).Set(Arg.Any<Expression<Func<TenantKey, DateTime>>>(), Arg.Any<DateTime>());
    }

    [Fact]
    public async Task UpdateKeyAsync_WithNullTenantKey_ShouldReturnError()
    {
        // Arrange
        TenantKey tenantKey = null!;
        var mockRepository = MockRepositoryWithKeys();
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpdateKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.UpdateFailed");
    }

    [Fact]
    public async Task UpdateKeyAsync_ShouldUpdateSpecificTenantOnly()
    {
        // Arrange
        var tenant1Key = new TenantKeyBuilder().WithTenantId("tenant-1").Build();
        var tenant2Key = new TenantKeyBuilder().WithTenantId("tenant-2").Build();
        var updatedTenant1Key = new TenantKeyBuilder()
            .WithTenantId("tenant-1")
            .WithPublicKey("-----BEGIN PUBLIC KEY-----\nUPDATED_TENANT1_KEY\n-----END PUBLIC KEY-----")
            .Build();

        var mockRepository = MockRepositoryWithKeys(tenant1Key, tenant2Key);
        var mockUpdateBuilder = Substitute.For<IHcmAggregateRootUpdateBuilder<TenantKey, TenantKeyId>>();
        mockRepository.AggregateRootUpdateBuilder(tenant1Key.Id).Returns(mockUpdateBuilder);
        mockRepository.UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>()).Returns(updatedTenant1Key);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpdateKeyAsync(updatedTenant1Key);

        // Assert
        result.IsError.Should().BeFalse();
        mockRepository.Received(1).AggregateRootUpdateBuilder(tenant1Key.Id);
        mockRepository.DidNotReceive().AggregateRootUpdateBuilder(tenant2Key.Id);
    }

    #endregion

    #region UpsertKeyAsync Tests

    [Fact]
    public async Task UpsertKeyAsync_WithNewTenantKey_ShouldCreateNewKey()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys();
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>()).Returns(tenantKey);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeFalse();
        result.Value.Should().NotBeNull();
        result.Value.TenantId.Should().Be(tenantKey.TenantId);
        await mockRepository.Received(1).AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task UpsertKeyAsync_WithExistingTenantKey_ShouldUpdateExistingKey()
    {
        // Arrange
        var existingKey = new TenantKeyBuilder().Build();
        var updatedKey = new TenantKeyBuilder()
            .WithTenantId(existingKey.TenantId.Value) // Use same tenant ID
            .WithPublicKey("-----BEGIN PUBLIC KEY-----\nUPDATED_KEY_DATA\n-----END PUBLIC KEY-----")
            .WithEncryptedPrivateKey("updated-encrypted-private-key")
            .Build();

        var mockRepository = MockRepositoryWithKeys(existingKey);
        var mockUpdateBuilder = Substitute.For<IHcmAggregateRootUpdateBuilder<TenantKey, TenantKeyId>>();
        mockRepository.AggregateRootUpdateBuilder(existingKey.Id).Returns(mockUpdateBuilder);
        mockRepository.UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>()).Returns(updatedKey);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpsertKeyAsync(updatedKey);

        // Assert
        result.IsError.Should().BeFalse();
        result.Value.Should().NotBeNull();
        mockRepository.Received(1).AggregateRootUpdateBuilder(existingKey.Id);
        await mockRepository.Received(1).UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task UpsertKeyAsync_WhenCreateFails_ShouldReturnCreateFailedError()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys();
        var createError = Error.Failure("Database.CreateFailed", "Failed to create entity");
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>()).Returns(createError);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.CreateFailed");
    }

    [Fact]
    public async Task UpsertKeyAsync_WhenUpdateFails_ShouldReturnUpdateFailedError()
    {
        // Arrange
        var existingKey = new TenantKeyBuilder().Build();
        var updatedKey = new TenantKeyBuilder().WithTenantId(existingKey.TenantId.Value).Build(); // Use same tenant ID
        var mockRepository = MockRepositoryWithKeys(existingKey);
        var mockUpdateBuilder = Substitute.For<IHcmAggregateRootUpdateBuilder<TenantKey, TenantKeyId>>();
        mockRepository.AggregateRootUpdateBuilder(existingKey.Id).Returns(mockUpdateBuilder);
        var updateError = Error.Failure("Database.UpdateFailed", "Failed to update entity");
        mockRepository.UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>()).Returns(updateError);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpsertKeyAsync(updatedKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.UpdateFailed");
    }

    [Fact]
    public async Task UpsertKeyAsync_WhenMongoExceptionThrown_ShouldReturnUpsertFailedError()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys();
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromException<ErrorOr<TenantKey>>(new MongoException("Database connection failed")));
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.CreateFailed");
        result.FirstError.Description.Should().Contain("MongoException");
    }

    [Fact]
    public async Task UpsertKeyAsync_WhenTimeoutExceptionThrown_ShouldReturnUpsertFailedError()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys();
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromException<ErrorOr<TenantKey>>(new TimeoutException("Operation timed out")));
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.CreateFailed");
        result.FirstError.Description.Should().Contain("TimeoutException");
    }

    [Fact]
    public async Task UpsertKeyAsync_WhenUnexpectedExceptionThrown_ShouldReturnUpsertFailedError()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys();
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromException<ErrorOr<TenantKey>>(new InvalidOperationException("Unexpected error")));
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Code.Should().Be("TenantKey.CreateFailed");
        result.FirstError.Description.Should().Contain("InvalidOperationException");
    }

    [Fact]
    public async Task UpsertKeyAsync_WhenGetKeyForTenantFails_ShouldReturnError()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = Substitute.For<IHcmRepository<TenantKey, TenantKeyId>>();
        mockRepository.When(x => x.GetAllAggregateRootsAsQueryable())
            .Do(x => throw new InvalidOperationException("Database error"));
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Should().Be(Errors.TenantKeyOperations.TenantKeyNotFound);
    }

    [Fact]
    public async Task UpsertKeyAsync_ShouldCallInsertForNewKey()
    {
        // Arrange
        var tenantKey = new TenantKeyBuilder().Build();
        var mockRepository = MockRepositoryWithKeys();
        mockRepository.AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>()).Returns(tenantKey);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpsertKeyAsync(tenantKey);

        // Assert
        result.IsError.Should().BeFalse();
        await mockRepository.Received(1).AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>());
        mockRepository.DidNotReceive().AggregateRootUpdateBuilder(Arg.Any<TenantKeyId>());
    }

    [Fact]
    public async Task UpsertKeyAsync_ShouldCallUpdateForExistingKey()
    {
        // Arrange
        var existingKey = new TenantKeyBuilder().Build();
        var updatedKey = new TenantKeyBuilder().WithTenantId(existingKey.TenantId.Value).Build();
        var mockRepository = MockRepositoryWithKeys(existingKey);
        var mockUpdateBuilder = Substitute.For<IHcmAggregateRootUpdateBuilder<TenantKey, TenantKeyId>>();
        mockRepository.AggregateRootUpdateBuilder(existingKey.Id).Returns(mockUpdateBuilder);
        mockRepository.UpdateAggregateRootAsync(mockUpdateBuilder, Arg.Any<CancellationToken>()).Returns(updatedKey);
        var repository = GetRepository(mockRepository);

        // Act
        var result = await repository.UpsertKeyAsync(updatedKey);

        // Assert
        result.IsError.Should().BeFalse();
        mockRepository.Received(1).AggregateRootUpdateBuilder(existingKey.Id);
        await mockRepository.DidNotReceive().AddAggregateRootV2Async(Arg.Any<TenantKey>(), Arg.Any<CancellationToken>());
    }

    #endregion

    #region GetKeyForTenantAsync Tests

    [Fact]
    public void GetKeyForTenantAsync_WithExistingTenant_ShouldReturnTenantKey()
    {
        // Arrange
        var tenantId = "test-tenant-123";
        var existingKey = new TenantKeyBuilder().WithTenantId(tenantId).Build();
        var mockRepository = MockRepositoryWithKeys(existingKey);
        var repository = GetRepository(mockRepository);

        // Act
        var result = repository.GetKeyForTenantAsync(tenantId);

        // Assert
        result.IsError.Should().BeFalse();
        result.Value.Should().NotBeNull();
        result.Value!.TenantId.Value.Should().Be(tenantId);
    }

    [Fact]
    public void GetKeyForTenantAsync_WithNonExistentTenant_ShouldReturnNull()
    {
        // Arrange
        var tenantId = "non-existent-tenant";
        var mockRepository = MockRepositoryWithKeys();
        var repository = GetRepository(mockRepository);

        // Act
        var result = repository.GetKeyForTenantAsync(tenantId);

        // Assert
        result.IsError.Should().BeFalse();
        result.Value.Should().BeNull();
    }
    
    [Fact]
    public void GetKeyForTenantAsync_WhenRepositoryThrowsException_ShouldReturnTenantKeyNotFoundError()
    {
        // Arrange
        var tenantId = "test-tenant-123";
        var mockRepository = Substitute.For<IHcmRepository<TenantKey, TenantKeyId>>();
        mockRepository.When(x => x.GetAllAggregateRootsAsQueryable())
            .Do(x => throw new InvalidOperationException("Database error"));
        var repository = GetRepository(mockRepository);

        // Act
        var result = repository.GetKeyForTenantAsync(tenantId);

        // Assert
        result.IsError.Should().BeTrue();
        result.FirstError.Should().Be(Errors.TenantKeyOperations.TenantKeyNotFound);
    }

    #endregion
}