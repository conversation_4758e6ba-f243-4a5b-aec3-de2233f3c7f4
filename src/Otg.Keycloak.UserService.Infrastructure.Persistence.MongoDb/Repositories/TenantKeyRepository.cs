using Otg.Keycloak.UserService.Application.Common.Interfaces.Persistence;
using Otg.Keycloak.UserService.Domain.Aggregates;
using ErrorOr;
using Otg.Keycloak.UserService.Infrastructure.Persistence.MongoDb.Common;
using Otg.Hcm.Infrastructure.Persistence.MongoDb;
using System.Diagnostics;
using Otg.Keycloak.UserService.Domain.ValueObjects;
using Otg.Hcm.Domain.ValueObjects;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Otg.Keycloak.UserService.Application.Common.Errors;

namespace Otg.Keycloak.UserService.Infrastructure.Persistence.MongoDb.Repositories;

/// <summary>
/// Repository for managing tenant RSA keys
/// </summary>
/// <param name="repositoryFactory"></param>
public class TenantKeyRepository(IHcmRepositoryFactory repositoryFactory) : ITenantKeyRepository
{
    private readonly IHcmRepository<TenantKey, TenantKeyId> _repository =
        repositoryFactory.CreateMultiTenantRepository<TenantKey, TenantKeyId>();

    #region TenantKey
    /// <summary>
    /// Inserts a new tenant key
    /// </summary>
    public async Task<ErrorOr<TenantKey>> InsertKeyAsync(TenantKey tenantKey, CancellationToken cancellationToken = default)
    {
        // Create a new activity to enable tracing this method
        using var activity = Common.Tracing.ActivitySources.InfrastructureLayerSource.StartActivity(Common.Tracing.WellKnownActivities.TenantKeyRepository.InsertTenantKey);

        try
        {
            tenantKey.CreatedAt = DateTime.UtcNow;
            tenantKey.UpdatedAt = DateTime.UtcNow;
            tenantKey.Id = TenantKeyId.Create();

            var addResult = await _repository.AddAggregateRootV2Async(tenantKey, cancellationToken);

            return addResult.MatchFirst<ErrorOr<TenantKey>>(
                addedData => addedData,
                error => Error.Failure("TenantKey.CreateFailed", $"Error code: {error.Code}, Description: {error.Description}")
            );
        }
        catch (Exception ex)
        {
            return Error.Failure("TenantKey.CreateFailed", $"Exception type: {ex.GetType().Name}, Message: {ex.Message}");
        }
    }

    /// <summary>
    /// Updates an existing tenant key
    /// </summary>
    public async Task<ErrorOr<TenantKey>> UpdateKeyAsync(TenantKey tenantKey, CancellationToken cancellationToken = default)
    {
        // Create a new activity to enable tracing this method
        using var activity = Common.Tracing.ActivitySources.InfrastructureLayerSource.StartActivity(Common.Tracing.WellKnownActivities.TenantKeyRepository.UpdateTenantKey);

        try
        {
            // Check if the key exists
            var existingKeyResult = GetKeyForTenantAsync(tenantKey.TenantId.Value, cancellationToken);

            if (existingKeyResult is { IsError: true })
            {
                return existingKeyResult.Errors;
            }

            if (existingKeyResult.Value is null)
            {
                return Error.NotFound("TenantKey.NotFound", $"Tenant key not found for tenant: {tenantKey.TenantId.Value}");
            }

            var existingKey = existingKeyResult.Value;
            tenantKey.UpdatedAt = DateTime.UtcNow;

            var builder = _repository.AggregateRootUpdateBuilder(existingKey.Id);
            builder.Set(k => k.PublicKey, tenantKey.PublicKey);
            builder.Set(k => k.EncryptedPrivateKey, tenantKey.EncryptedPrivateKey);
            builder.Set(k => k.UpdatedAt, DateTime.UtcNow);

            var result = await _repository.UpdateAggregateRootAsync(builder, cancellationToken);
            return result.MatchFirst<ErrorOr<TenantKey>>(
                data => tenantKey,
                error => Error.Failure("TenantKey.UpdateFailed", $"Error: {error.Code} - {error.Description}")
            );
        }
        catch (Exception ex)
        {
            return Error.Failure("TenantKey.UpdateFailed", $"Unhandled exception: {ex.GetType().Name} - {ex.Message}");
        }
    }

    /// <summary>
    /// Upserts a tenant key (creates if not exists, updates if exists)
    /// </summary>
    public async Task<ErrorOr<TenantKey>> UpsertKeyAsync(TenantKey tenantKey, CancellationToken cancellationToken = default)
    {
        // Create a new activity to enable tracing this method
        using var activity = Common.Tracing.ActivitySources.InfrastructureLayerSource.StartActivity(Common.Tracing.WellKnownActivities.TenantKeyRepository.UpsertTenantKey);

        try
        {
            // Check if the key already exists
            var existingKeyResult = GetKeyForTenantAsync(tenantKey.TenantId.Value, cancellationToken);

            if (existingKeyResult is { IsError: true })
            {
                return existingKeyResult.Errors;
            }

            if (existingKeyResult.Value is not null)
            {
                // Update existing key
                return await UpdateKeyAsync(tenantKey, cancellationToken);
            }
            else
            {
                // Insert new key
                return await InsertKeyAsync(tenantKey, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            return Error.Failure("TenantKey.UpsertFailed", $"Unhandled exception: {ex.GetType().Name} - {ex.Message}");
        }
    }

    /// <summary>
    /// Gets the key for a tenant
    /// </summary>
    public ErrorOr<TenantKey?> GetKeyForTenantAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var keysQueryable = _repository.GetAllAggregateRootsAsQueryable();
            return keysQueryable.FirstOrDefault(c => c.TenantId.Value.Equals(tenantId, StringComparison.CurrentCultureIgnoreCase));
        }
        catch (Exception ex)
        {
            return Errors.TenantKeyOperations.TenantKeyNotFound;
        }
    }
    #endregion
}
